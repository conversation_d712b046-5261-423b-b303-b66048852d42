"""
质量回查模块 - 自动检测标注质量和数据一致性
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
import Levenshtein
from config import QUALITY_CONFIG


class QualityChecker:
    """质量检查器"""
    
    def __init__(self, config=None):
        self.config = config or QUALITY_CONFIG
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("QualityChecker")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def calculate_edit_distance_ratio(self, text1: str, text2: str) -> float:
        """计算编辑距离比例"""
        if not text1 and not text2:
            return 0.0
        if not text1 or not text2:
            return 1.0
            
        distance = Levenshtein.distance(text1, text2)
        max_len = max(len(text1), len(text2))
        
        return distance / max_len if max_len > 0 else 0.0
    
    def check_invalid_patterns(self, text: str) -> List[str]:
        """检查无效文本模式"""
        invalid_patterns = []
        
        for pattern in self.config["invalid_patterns"]:
            if re.search(pattern, text, re.IGNORECASE):
                invalid_patterns.append(pattern)
                
        return invalid_patterns
    
    def check_text_in_original(self, predicted_text: str, original_text: str) -> Dict:
        """检查预测文本是否在原文中"""
        # 清理文本
        pred_clean = self._clean_text(predicted_text)
        orig_clean = self._clean_text(original_text)
        
        # 检查是否完全包含
        exact_match = pred_clean in orig_clean
        
        # 检查行级匹配
        orig_lines = [line.strip() for line in orig_clean.split('\n') if line.strip()]
        line_matches = []
        
        for i, line in enumerate(orig_lines):
            if pred_clean in line:
                line_matches.append({
                    "line_idx": i,
                    "line_text": line,
                    "match_ratio": len(pred_clean) / len(line) if line else 0
                })
        
        # 计算最佳匹配的编辑距离
        best_edit_ratio = 1.0
        best_match_line = None
        
        for line in orig_lines:
            ratio = self.calculate_edit_distance_ratio(pred_clean, line)
            if ratio < best_edit_ratio:
                best_edit_ratio = ratio
                best_match_line = line
        
        return {
            "exact_match": exact_match,
            "line_matches": line_matches,
            "best_edit_ratio": best_edit_ratio,
            "best_match_line": best_match_line,
            "predicted_clean": pred_clean,
            "original_clean": orig_clean
        }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        # 移除特殊字符（保留中英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()（）。，！？；：""''【】]', '', text)
        return text
    
    def validate_annotation(self, result: Dict, original_text: str) -> Dict:
        """验证单个标注结果 - 支持新的行级格式"""
        validation = {
            "is_valid": True,
            "issues": [],
            "confidence": 1.0,
            "result": result
        }

        status = result.get("status", "")
        lines = result.get("lines", [])
        text = result.get("text", "")

        # 检查状态
        if status not in ["ok", "skip", "error"]:
            validation["is_valid"] = False
            validation["issues"].append(f"Invalid status: {status}")
            return validation

        # 如果是skip或error，直接返回
        if status in ["skip", "error"]:
            validation["confidence"] = 0.0 if status == "error" else 0.5
            return validation

        # 对于ok状态，进行详细检查
        if status == "ok":
            # 检查是否有行数据
            if not lines:
                validation["is_valid"] = False
                validation["issues"].append("No lines found in ok status")
                return validation

            # 逐行验证
            line_validations = []
            total_confidence = 0.0

            for line in lines:
                line_text = line.get("text", "")
                line_validation = self._validate_single_line(line_text, original_text)
                line_validations.append(line_validation)
                total_confidence += line_validation["confidence"]

                if not line_validation["is_valid"]:
                    validation["is_valid"] = False
                    validation["issues"].extend(line_validation["issues"])

            # 计算平均置信度
            validation["confidence"] = total_confidence / len(lines) if lines else 0.0
            validation["line_validations"] = line_validations



        return validation

    def _validate_single_line(self, line_text: str, original_text: str) -> Dict:
        """验证单行文本"""
        validation = {
            "is_valid": True,
            "issues": [],
            "confidence": 1.0
        }

        # 检查文本长度
        if len(line_text) < self.config["min_text_length"]:
            validation["is_valid"] = False
            validation["issues"].append(f"Line too short: {len(line_text)} chars")

        # 检查无效模式
        invalid_patterns = self.check_invalid_patterns(line_text)
        if invalid_patterns:
            validation["is_valid"] = False
            validation["issues"].append(f"Invalid patterns in line: {invalid_patterns}")

        # 检查文本匹配度
        match_info = self.check_text_in_original(line_text, original_text)
        edit_ratio = match_info["best_edit_ratio"]

        if edit_ratio > self.config["max_edit_distance_ratio"]:
            validation["is_valid"] = False
            validation["issues"].append(
                f"High edit distance: {edit_ratio:.3f} > {self.config['max_edit_distance_ratio']}"
            )

        # 设置置信度
        if match_info["exact_match"]:
            validation["confidence"] = 1.0
        elif match_info["line_matches"]:
            # 基于行匹配计算置信度
            best_line_ratio = max(m["match_ratio"] for m in match_info["line_matches"])
            validation["confidence"] = best_line_ratio * (1 - edit_ratio)
        else:
            validation["confidence"] = max(0.1, 1 - edit_ratio)

        validation["match_info"] = match_info
        return validation
    
    def batch_validate(self, results: List[Dict], original_text: str) -> Dict:
        """批量验证标注结果 - 支持新的行级格式"""
        validations = []
        stats = {
            "total": len(results),
            "valid": 0,
            "invalid": 0,
            "ok": 0,
            "skip": 0,
            "error": 0,
            "avg_confidence": 0.0,
            "total_lines": 0
        }

        total_confidence = 0.0
        total_lines = 0

        for result in results:
            validation = self.validate_annotation(result, original_text)
            validations.append(validation)

            # 统计
            if validation["is_valid"]:
                stats["valid"] += 1
            else:
                stats["invalid"] += 1

            status = result.get("status", "")
            if status in stats:
                stats[status] += 1

            # 统计行数
            lines = result.get("lines", [])
            total_lines += len(lines)

            total_confidence += validation["confidence"]

        stats["avg_confidence"] = total_confidence / len(results) if results else 0.0
        stats["total_lines"] = total_lines

        # 保存所有结果（不过滤）
        all_results = [v["result"] for v in validations]

        # 分类结果
        valid_results = [
            v["result"] for v in validations
            if v["is_valid"] and v["result"].get("status") == "ok"
        ]

        skip_results = [
            v["result"] for v in validations
            if v["result"].get("status") == "skip"
        ]

        error_results = [
            v["result"] for v in validations
            if v["result"].get("status") == "error" or not v["is_valid"]
        ]

        self.logger.info(f"Validation stats: {stats}")

        return {
            "validations": validations,
            "stats": stats,
            "all_results": all_results,      # 所有结果
            "valid_results": valid_results,  # 有效结果
            "skip_results": skip_results,    # 跳过结果
            "error_results": error_results   # 错误结果
        }
    
    def generate_quality_report(self, validation_result: Dict, 
                              image_path: str) -> Dict:
        """生成质量报告"""
        stats = validation_result["stats"]
        validations = validation_result["validations"]
        
        # 收集问题
        all_issues = []
        for v in validations:
            if not v["is_valid"]:
                all_issues.extend(v["issues"])
        
        # 计算质量分数
        quality_score = stats["valid"] / stats["total"] if stats["total"] > 0 else 0.0
        quality_score = quality_score * stats["avg_confidence"]
        
        report = {
            "image_path": image_path,
            "quality_score": quality_score,
            "total_crops": stats["total"],
            "valid_annotations": stats["valid"],
            "usable_annotations": len(validation_result["valid_results"]),
            "avg_confidence": stats["avg_confidence"],
            "status_distribution": {
                "ok": stats["ok"],
                "skip": stats["skip"], 
                "error": stats["error"]
            },
            "common_issues": list(set(all_issues)),
            "recommendation": self._get_recommendation(quality_score, stats)
        }
        
        return report
    
    def _get_recommendation(self, quality_score: float, stats: Dict) -> str:
        """获取质量改进建议"""
        if quality_score >= 0.8:
            return "质量良好，可以直接使用"
        elif quality_score >= 0.6:
            return "质量中等，建议人工复查部分结果"
        elif quality_score >= 0.4:
            return "质量较差，需要调整裁切参数或Prompt"
        else:
            return "质量很差，建议重新处理"


def test_quality_checker():
    """测试质量检查器"""
    checker = QualityChecker()
    
    # 测试数据
    original_text = """三、写作（60分）

20. 阅读下面的材料，按要求写作。（60分）

正在热播的一档音乐节目在宣传语里写道："赢，只是歌手的一段路；迎，才是音乐的目的地。"这句话谈的是音乐，也能引发音乐之外的思考。"""
    
    test_results = [
        {"status": "ok", "text": "三、写作（60分）"},
        {"status": "ok", "text": "20. 阅读下面的材料，按要求写作。（60分）"},
        {"status": "skip", "text": ""},
        {"status": "ok", "text": "答题卡内答题"},  # 应该被标记为无效
        {"status": "ok", "text": "正在热播的一档音乐节目"}
    ]
    
    print("测试质量检查器...")
    validation_result = checker.batch_validate(test_results, original_text)
    report = checker.generate_quality_report(validation_result, "test_image.png")
    
    print(f"质量报告: {report}")


if __name__ == "__main__":
    test_quality_checker()
