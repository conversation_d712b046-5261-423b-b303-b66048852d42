"""
配置文件 - 数据增强系统参数设置
"""

# 火山引擎豆包API配置
DOUBAO_CONFIG = {
    "api_key": "6a721569-9881-441e-bfcd-0571e5d09b6e",
    "model": "doubao-seed-1-6-thinking-250715",  # 使用正确的模型ID
    "timeout": 1800  # 30分钟超时
}

# 随机裁切参数
CROP_CONFIG = {
    "num_crops_per_image": 6,  # 每张图片生成的裁切数量
    "scale_range": (0.4, 0.9),  # 裁切尺度范围
    "area_range": (0.2, 0.8),   # 面积比例范围
    "text_ratio_threshold": 0.05,  # 文字占比阈值
    "max_iou": 0.5,  # 最大重叠度
    "expand_bbox": 5  # bbox扩展像素
}

# 质量回查参数
QUALITY_CONFIG = {
    "max_edit_distance_ratio": 0.05,  # 最大编辑距离比例
    "min_text_length": 3,  # 最小文本长度
    "invalid_patterns": [  # 无效文本模式
        r"答题卡",
        r"请在.*答题",
        r"考试时间",
        r"姓名.*学号",
        r"^\s*$"  # 空白
    ]
}

# 文件路径配置
PATH_CONFIG = {
    "input_images": "processed_data/images",
    "input_texts": "processed_data/answers",
    "output_dir": "augmented_data",
    "crops_dir": "augmented_data/crops",
    "annotations_dir": "augmented_data/annotations",
    "visualizations_dir": "augmented_data/visualizations",
    "logs_dir": "logs"
}

# 优化的Prompt模板 - 基于用户最新提供的模板
PROMPT_TEMPLATE = """# 角色
你是一名 OCR 多模态助手，仅能陈述子图中**肉眼可见**的字符。

# 输入
- 子图（image_url 已嵌入）
- full_text（原文全文）:
\"\"\"
{full_text}
\"\"\"

# 关键规则（必须逐条遵守）
1. **严格视觉边界**：当某行或某句在子图边缘被截断时，
   - **立即停止**输出该行剩余字符，
   - 只保留子图中真正可见的部分，不得推测或续写。
2. **逐字符对齐**：输出内容必须与 full_text 中对应字符 **逐字一致**。
3. **不能添字、不能漏字**：看见多少写多少；看不见的一个字也不能写。
4. **禁止 hallucination**：不得输出 full_text 中但子图里不可见的任何字符；也不得输出与作文无关内容、空白或提示语。
5. 若子图中没有任何可见字符，返回空数组 `[]`。

# 输出格式（只能是 JSON 数组）
```json
[
  {{"id":1, "text":"可见片段1"}},
  {{"id":2, "text":"可见片段2"}}
]
```"""
