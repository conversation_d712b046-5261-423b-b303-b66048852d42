#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的配置
"""

from config import DOUBAO_CONFIG, PROMPT_TEMPLATE

def test_config():
    """测试配置是否正确"""
    print("=" * 60)
    print("🧪 测试修复后的配置")
    print("=" * 60)
    
    # 测试DOUBAO_CONFIG
    print("📋 豆包API配置:")
    for key, value in DOUBAO_CONFIG.items():
        if key == "api_key":
            print(f"   {key}: {'*' * 20}{value[-8:] if len(value) > 8 else '***'}")
        else:
            print(f"   {key}: {value}")
    
    # 测试PROMPT_TEMPLATE
    print(f"\n📝 提示词模板:")
    print("   模板长度:", len(PROMPT_TEMPLATE))
    print("   包含{full_text}占位符:", "{full_text}" in PROMPT_TEMPLATE)
    
    # 测试模板格式化
    test_text = "这是一个测试文本"
    try:
        formatted_prompt = PROMPT_TEMPLATE.format(full_text=test_text)
        print("   ✅ 模板格式化成功")
        print(f"   格式化后长度: {len(formatted_prompt)}")
    except Exception as e:
        print(f"   ❌ 模板格式化失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 配置测试通过!")
    print("🚀 可以开始测试API连接")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_config()
