
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据增强结果报告 - 49_s</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-box { background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }
        .images { display: flex; gap: 20px; margin: 20px 0; }
        .image-container { flex: 1; }
        .image-container img { max-width: 100%; height: auto; border: 1px solid #ddd; }
        .crops-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .crops-table th, .crops-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .crops-table th { background-color: #f2f2f2; }
        .status-ok { color: green; font-weight: bold; }
        .status-skip { color: orange; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .text-content { max-width: 300px; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据增强结果报告</h1>
        <p><strong>图片:</strong> 49_s.jpg</p>
        <p><strong>生成时间:</strong> 2025-07-30 00:42:49</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>总裁切数</h3>
            <p style="font-size: 24px; margin: 0;">6</p>
        </div>
        <div class="stat-box">
            <h3>成功识别</h3>
            <p style="font-size: 24px; margin: 0; color: green;">0</p>
        </div>
        <div class="stat-box">
            <h3>跳过</h3>
            <p style="font-size: 24px; margin: 0; color: orange;">0</p>
        </div>
        <div class="stat-box">
            <h3>错误</h3>
            <p style="font-size: 24px; margin: 0; color: red;">0</p>
        </div>
    </div>
    
    <div class="images">
        <div class="image-container">
            <h3>原始图片</h3>
            <img src="..\..\..\processed_data\images\49_s.jpg" alt="原始图片">
        </div>
        <div class="image-container">
            <h3>可视化结果</h3>
            <img src="49_s_visualization.jpg" alt="可视化结果">
        </div>
    </div>
    
    <h2>详细结果</h2>
    <table class="crops-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>状态</th>
                <th>边界框</th>
                <th>尺寸</th>
                <th>文字占比</th>
                <th>识别文本</th>
            </tr>
        </thead>
        <tbody>

            <tr>
                <td>1</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(333, 123, 1827, 1005)</td>
                <td>1494×882</td>
                <td>0.233</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>2</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(13, 341, 983, 915)</td>
                <td>970×574</td>
                <td>0.248</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>3</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(150, 130, 1147, 720)</td>
                <td>997×590</td>
                <td>0.251</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>4</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(644, 137, 1600, 703)</td>
                <td>956×566</td>
                <td>0.232</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>5</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(110, 535, 946, 1030)</td>
                <td>836×495</td>
                <td>0.257</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>6</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(356, 421, 1308, 985)</td>
                <td>952×564</td>
                <td>0.239</td>
                <td class="text-content"></td>
            </tr>

        </tbody>
    </table>
</body>
</html>
