"""
可视化模块 - 在图片上绘制裁切框和识别结果
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple
import json


class Visualizer:
    """可视化器"""
    
    def __init__(self):
        self.colors = [
            (255, 0, 0),    # 红色 - error
            (0, 255, 0),    # 绿色 - ok
            (255, 255, 0),  # 黄色 - skip
            (0, 0, 255),    # 蓝色 - 其他
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 青色
        ]
        
    def get_color_by_status(self, api_success: bool) -> Tuple[int, int, int]:
        """根据API调用状态获取颜色"""
        if api_success:
            return (0, 255, 0)  # 绿色 - 成功
        else:
            return (255, 0, 0)  # 红色 - 失败
    
    def draw_bbox_on_image(self, image_path: str, crops_data: List[Dict], 
                          api_results: List[Dict], output_path: str) -> str:
        """在图片上绘制边界框和识别结果"""
        # 读取原图
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        height, width = image.shape[:2]
        
        # 创建副本用于绘制
        vis_image = image.copy()
        
        # 绘制每个裁切框
        for i, (crop, result) in enumerate(zip(crops_data, api_results)):
            bbox = crop.get("bbox", [0, 0, 0, 0])
            x1, y1, x2, y2 = bbox

            # 获取状态和颜色
            api_success = result.get("api_success", False)
            color = self.get_color_by_status(api_success)

            # 绘制边界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)

            # 绘制编号
            cv2.putText(vis_image, f"{i+1}", (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

            # 绘制状态标签
            status_text = "OK" if api_success else "FAIL"
            cv2.putText(vis_image, status_text, (x1, y2+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 添加图例
        self._add_legend(vis_image)
        
        # 保存可视化结果
        cv2.imwrite(output_path, vis_image)
        return output_path
    
    def _add_legend(self, image: np.ndarray):
        """添加图例"""
        height, width = image.shape[:2]
        
        # 图例位置（右上角）
        legend_x = width - 200
        legend_y = 30
        
        # 绘制图例背景
        cv2.rectangle(image, (legend_x-10, legend_y-10), 
                     (width-10, legend_y+80), (255, 255, 255), -1)
        cv2.rectangle(image, (legend_x-10, legend_y-10), 
                     (width-10, legend_y+80), (0, 0, 0), 1)
        
        # 绘制图例项
        legend_items = [
            ("SUCCESS", (0, 255, 0)),
            ("FAILED", (255, 0, 0))
        ]
        
        for i, (label, color) in enumerate(legend_items):
            y_pos = legend_y + i * 20
            cv2.rectangle(image, (legend_x, y_pos), (legend_x+15, y_pos+15), color, -1)
            cv2.putText(image, label, (legend_x+20, y_pos+12), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
    
    def create_detailed_visualization(self, image_path: str, crops_data: List[Dict], 
                                    api_results: List[Dict], output_dir: str, 
                                    base_name: str) -> Dict:
        """创建详细的可视化结果"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 绘制主要可视化图片
        main_vis_path = os.path.join(output_dir, f"{base_name}_visualization.jpg")
        self.draw_bbox_on_image(image_path, crops_data, api_results, main_vis_path)
        
        # 2. 保存每个裁切的详细信息
        crops_info = []
        for i, (crop, result) in enumerate(zip(crops_data, api_results)):
            crop_info = {
                "crop_id": i + 1,
                "bbox": crop.get("bbox", []),
                "text_ratio": crop.get("text_ratio", 0),
                "size": crop.get("size", [0, 0]),
                "status": result.get("status", "unknown"),
                "recognized_lines": result.get("lines", []),
                "recognized_text": result.get("text", ""),
                "format": result.get("format", "unknown")
            }
            crops_info.append(crop_info)
        
        # 3. 保存裁切详情JSON
        crops_detail_path = os.path.join(output_dir, f"{base_name}_crops_detail.json")
        with open(crops_detail_path, 'w', encoding='utf-8') as f:
            json.dump(crops_info, f, ensure_ascii=False, indent=2)
        
        # 4. 生成HTML报告
        html_path = self._create_html_report(
            image_path, main_vis_path, crops_info, output_dir, base_name
        )
        
        return {
            "visualization_image": main_vis_path,
            "crops_detail": crops_detail_path,
            "html_report": html_path,
            "crops_info": crops_info
        }
    
    def _create_html_report(self, original_image: str, vis_image: str, 
                           crops_info: List[Dict], output_dir: str, 
                           base_name: str) -> str:
        """创建HTML报告"""
        html_path = os.path.join(output_dir, f"{base_name}_report.html")
        
        # 计算统计信息
        total_crops = len(crops_info)
        ok_count = sum(1 for crop in crops_info if crop["status"] == "ok")
        skip_count = sum(1 for crop in crops_info if crop["status"] == "skip")
        error_count = sum(1 for crop in crops_info if crop["status"] == "error")
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据增强结果报告 - {base_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .images {{ display: flex; gap: 20px; margin: 20px 0; }}
        .image-container {{ flex: 1; }}
        .image-container img {{ max-width: 100%; height: auto; border: 1px solid #ddd; }}
        .crops-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .crops-table th, .crops-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .crops-table th {{ background-color: #f2f2f2; }}
        .status-ok {{ color: green; font-weight: bold; }}
        .status-skip {{ color: orange; font-weight: bold; }}
        .status-error {{ color: red; font-weight: bold; }}
        .text-content {{ max-width: 300px; word-wrap: break-word; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>数据增强结果报告</h1>
        <p><strong>图片:</strong> {os.path.basename(original_image)}</p>
        <p><strong>生成时间:</strong> {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>总裁切数</h3>
            <p style="font-size: 24px; margin: 0;">{total_crops}</p>
        </div>
        <div class="stat-box">
            <h3>成功识别</h3>
            <p style="font-size: 24px; margin: 0; color: green;">{ok_count}</p>
        </div>
        <div class="stat-box">
            <h3>跳过</h3>
            <p style="font-size: 24px; margin: 0; color: orange;">{skip_count}</p>
        </div>
        <div class="stat-box">
            <h3>错误</h3>
            <p style="font-size: 24px; margin: 0; color: red;">{error_count}</p>
        </div>
    </div>
    
    <div class="images">
        <div class="image-container">
            <h3>原始图片</h3>
            <img src="{os.path.relpath(original_image, output_dir)}" alt="原始图片">
        </div>
        <div class="image-container">
            <h3>可视化结果</h3>
            <img src="{os.path.relpath(vis_image, output_dir)}" alt="可视化结果">
        </div>
    </div>
    
    <h2>详细结果</h2>
    <table class="crops-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>状态</th>
                <th>边界框</th>
                <th>尺寸</th>
                <th>文字占比</th>
                <th>识别文本</th>
            </tr>
        </thead>
        <tbody>
"""
        
        for crop in crops_info:
            status_class = f"status-{crop['status']}"
            bbox_str = f"({crop['bbox'][0]}, {crop['bbox'][1]}, {crop['bbox'][2]}, {crop['bbox'][3]})"
            size_str = f"{crop['size'][0]}×{crop['size'][1]}"
            text_ratio = f"{crop['text_ratio']:.3f}"
            recognized_text = crop['recognized_text'].replace('\n', '<br>')
            
            html_content += f"""
            <tr>
                <td>{crop['crop_id']}</td>
                <td class="{status_class}">{crop['status'].upper()}</td>
                <td>{bbox_str}</td>
                <td>{size_str}</td>
                <td>{text_ratio}</td>
                <td class="text-content">{recognized_text}</td>
            </tr>
"""
        
        html_content += """
        </tbody>
    </table>
</body>
</html>
"""
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_path


def test_visualization():
    """测试可视化功能"""
    visualizer = Visualizer()
    
    # 模拟数据
    crops_data = [
        {"bbox": [100, 100, 300, 150], "text_ratio": 0.3, "size": [200, 50]},
        {"bbox": [100, 200, 400, 250], "text_ratio": 0.5, "size": [300, 50]},
    ]
    
    api_results = [
        {"status": "ok", "lines": [{"id": 1, "text": "测试文本1"}], "text": "测试文本1"},
        {"status": "skip", "lines": [], "text": ""},
    ]
    
    print("可视化模块测试完成")


if __name__ == "__main__":
    test_visualization()
