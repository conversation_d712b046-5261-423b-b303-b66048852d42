# 手写OCR数据增强系统

基于**随机裁切➜模型标注➜质量回查**三段流水线的手写OCR数据增强解决方案，集成火山引擎豆包1.6思考模型API。

## 功能特点

- **🎯 行级精确标注**: 只输出完整行，自动过滤半行和无效内容
- **🖼️ 智能随机裁切**: 边缘外扩算法，降低截半行风险
- **🤖 AI深度思考**: 集成豆包1.6深度思考模型，精确文本对齐
- **✅ 自动质量回查**: 多维度质量检测，确保标注一致性
- **📈 可视化展示**: 自动生成裁切框标注图和HTML报告
- **💾 完整结果保存**: 保存所有识别结果，包括成功、跳过、错误状态
- **⚡ 批量并发处理**: 支持多线程并发，提高处理效率
- **📊 详细日志报告**: 完整的处理日志和质量分析报告

## 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装火山引擎SDK（最新版本）
pip install -U 'volcengine-python-sdk[ark]'
```

## 配置说明

在 `config.py` 中配置相关参数：

1. **API配置**: 设置豆包API密钥和参数
2. **裁切参数**: 调整随机裁切的尺度、数量等
3. **质量阈值**: 设置文本匹配度、编辑距离等阈值
4. **文件路径**: 配置输入输出目录

## 使用方法

### 快速开始

```bash
# 运行完整示例（推荐首次使用）
python example_usage.py

# 或直接运行测试模式
python data_augmentation_pipeline.py --test
```

### 详细使用

1. **测试模式（推荐首次使用）**
```bash
python data_augmentation_pipeline.py --test
```

2. **处理指定数量图片**
```bash
python data_augmentation_pipeline.py --max_images 10
```

3. **处理所有图片**
```bash
python data_augmentation_pipeline.py
```

## 数据格式要求

## 项目结构

```
数据增强api/
├── config.py                           # 配置文件
├── data_augmentation_pipeline.py       # 主流水线
├── random_crop.py                      # 随机裁切模块
├── doubao_api.py                       # 豆包API调用模块
├── quality_checker.py                  # 质量检查模块
├── visualization.py                    # 可视化模块
├── example_usage.py                    # 测试示例
├── requirements.txt                    # 依赖包列表
├── README.md                          # 说明文档
├── processed_data/                    # 输入数据
│   ├── images/                        # 图片文件
│   └── answers/                       # 文本标注
├── augmented_data/                    # 输出结果
│   └── visualizations/                # 可视化结果
└── logs/                             # 日志文件
```

### 输入数据格式
- 图片文件：`processed_data/images/` 目录下的 PNG/JPG 文件
- 文本文件：`processed_data/answers/` 目录下的对应 `*_answer.txt` 文件

### 输出结果格式
- **主结果文件**：`augmented_data/augmented_data_*.json`
- **可视化结果**：`augmented_data/visualizations/[图片名]/`
  - `*_visualization.jpg` - 带标注框的可视化图片
  - `*_crops_detail.json` - 详细裁切信息
  - `*_report.html` - 交互式HTML报告
- **处理日志**：`logs/pipeline_*.log`
- **汇总报告**：`logs/summary_report_*.txt`

## 输出结果说明

### 主要结果文件 (JSON格式)
- `metadata`: 处理统计信息
- `results`: 每张图片的详细处理结果
  - `crops`: 裁切信息和base64数据
  - `api_results`: 模型标注结果
  - `validation_result`: 质量验证结果
  - `quality_report`: 质量分析报告

### 质量分数说明
- **>0.8**: 高质量，可直接使用
- **0.6-0.8**: 中等质量，建议人工复查
- **<0.6**: 低质量，需要调整参数

## 参数调优建议

### 提高标注质量
1. 调整 `CROP_CONFIG.text_ratio_threshold` 提高文字占比要求
2. 减少 `CROP_CONFIG.num_crops_per_image` 降低重叠概率
3. 优化 `PROMPT_TEMPLATE` 提高模型理解准确性

### 提高处理效率
1. 调整 `DOUBAO_CONFIG.max_concurrent` 控制并发数
2. 减少 `CROP_CONFIG.num_crops_per_image` 降低API调用次数
3. 设置 `max_images` 参数分批处理

## 常见问题

### Q: API调用失败怎么办？
A: 检查网络连接和API密钥，查看日志文件获取详细错误信息。

### Q: 生成的裁切质量不好？
A: 调整 `CROP_CONFIG` 中的参数，特别是 `text_ratio_threshold` 和 `scale_range`。

### Q: 模型标注不准确？
A: 优化 `PROMPT_TEMPLATE`，或调整 `DOUBAO_CONFIG` 中的温度参数。

## 技术架构

```
输入图片 → 随机裁切 → 豆包API标注 → 质量回查 → 输出结果
    ↓         ↓           ↓          ↓         ↓
  原始图片   子图base64   结构化JSON   验证过滤   增强数据集
```

## 许可证

MIT License
