#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时保存功能的流水线
"""

from data_augmentation_pipeline import DataAugmentationPipeline

def main():
    """测试实时保存功能"""
    print("=" * 60)
    print("🧪 测试实时保存功能")
    print("=" * 60)
    
    try:
        # 创建流水线
        pipeline = DataAugmentationPipeline()
        
        # 只处理一张图片进行测试
        print("🔄 开始处理（只处理一张s后缀图片）...")
        pipeline.run_pipeline(max_images=1)
        
        print("\n" + "=" * 60)
        print("✅ 测试完成!")
        print("📁 请检查以下目录:")
        print("   - augmented_data/crops/        (裁切图片和结果文件)")
        print("   - augmented_data/visualizations/ (可视化结果)")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
