#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强流水线启动脚本
只处理文件名以 '_s' 结尾的图片文件
"""

import os
import sys
from data_augmentation_pipeline import DataAugmentationPipeline

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 数据增强流水线启动")
    print("=" * 60)
    
    # 检查必要的目录
    required_dirs = [
        "processed_data/images",
        "processed_data/answers"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            print(f"❌ 错误: 缺少必要目录 {dir_path}")
            print("请确保以下目录存在并包含数据:")
            print("  - processed_data/images/     (包含 *_s.png 图片文件)")
            print("  - processed_data/answers/    (包含对应的 *_s_answer.txt 文件)")
            return
    
    # 检查是否有s后缀的图片文件
    images_dir = "processed_data/images"
    s_images = [f for f in os.listdir(images_dir) if f.endswith('_s.png')]
    
    if not s_images:
        print("❌ 错误: 在 processed_data/images/ 目录中没有找到 '_s.png' 结尾的图片文件")
        print("请确保图片文件名格式为: xxxxx_s.png")
        return
    
    print(f"✅ 找到 {len(s_images)} 个s后缀图片文件:")
    for img in s_images[:5]:  # 只显示前5个
        print(f"   - {img}")
    if len(s_images) > 5:
        print(f"   ... 还有 {len(s_images) - 5} 个文件")
    
    # 询问用户是否继续
    print("\n" + "=" * 60)
    choice = input("是否开始处理? (y/n): ").lower().strip()
    
    if choice != 'y':
        print("❌ 用户取消操作")
        return
    
    try:
        # 创建并运行流水线
        print("\n🔄 初始化流水线...")
        pipeline = DataAugmentationPipeline()
        
        print("🔄 开始处理...")
        pipeline.run_pipeline()
        
        print("\n" + "=" * 60)
        print("✅ 处理完成!")
        print("📁 结果文件位置:")
        print("   - augmented_data/              (主要结果文件)")
        print("   - augmented_data/crops/        (裁切的小图片)")
        print("   - augmented_data/visualizations/ (可视化结果)")
        print("   - logs/                        (日志文件)")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
