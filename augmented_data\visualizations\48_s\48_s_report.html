
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据增强结果报告 - 48_s</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-box { background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }
        .images { display: flex; gap: 20px; margin: 20px 0; }
        .image-container { flex: 1; }
        .image-container img { max-width: 100%; height: auto; border: 1px solid #ddd; }
        .crops-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .crops-table th, .crops-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .crops-table th { background-color: #f2f2f2; }
        .status-ok { color: green; font-weight: bold; }
        .status-skip { color: orange; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .text-content { max-width: 300px; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据增强结果报告</h1>
        <p><strong>图片:</strong> 48_s.jpg</p>
        <p><strong>生成时间:</strong> 2025-07-30 00:31:14</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>总裁切数</h3>
            <p style="font-size: 24px; margin: 0;">6</p>
        </div>
        <div class="stat-box">
            <h3>成功识别</h3>
            <p style="font-size: 24px; margin: 0; color: green;">0</p>
        </div>
        <div class="stat-box">
            <h3>跳过</h3>
            <p style="font-size: 24px; margin: 0; color: orange;">0</p>
        </div>
        <div class="stat-box">
            <h3>错误</h3>
            <p style="font-size: 24px; margin: 0; color: red;">0</p>
        </div>
    </div>
    
    <div class="images">
        <div class="image-container">
            <h3>原始图片</h3>
            <img src="..\..\..\processed_data\images\48_s.jpg" alt="原始图片">
        </div>
        <div class="image-container">
            <h3>可视化结果</h3>
            <img src="48_s_visualization.jpg" alt="可视化结果">
        </div>
    </div>
    
    <h2>详细结果</h2>
    <table class="crops-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>状态</th>
                <th>边界框</th>
                <th>尺寸</th>
                <th>文字占比</th>
                <th>识别文本</th>
            </tr>
        </thead>
        <tbody>

            <tr>
                <td>1</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(123, 731, 730, 1803)</td>
                <td>607×1072</td>
                <td>0.263</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>2</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(164, 58, 761, 1113)</td>
                <td>597×1055</td>
                <td>0.216</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>3</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(107, 218, 1065, 1914)</td>
                <td>958×1696</td>
                <td>0.245</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>4</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(447, 243, 1079, 1359)</td>
                <td>632×1116</td>
                <td>0.254</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>5</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(61, 351, 637, 1367)</td>
                <td>576×1016</td>
                <td>0.267</td>
                <td class="text-content"></td>
            </tr>

            <tr>
                <td>6</td>
                <td class="status-unknown">UNKNOWN</td>
                <td>(446, 961, 967, 1879)</td>
                <td>521×918</td>
                <td>0.252</td>
                <td class="text-content"></td>
            </tr>

        </tbody>
    </table>
</body>
</html>
