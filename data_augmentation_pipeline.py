"""
数据增强主流水线 - 整合随机裁切、模型标注、质量回查
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple
from tqdm import tqdm

from random_crop import RandomCropper
from doubao_api import <PERSON>ubaoAP<PERSON>lient
from quality_checker import <PERSON><PERSON>he<PERSON>
from visualization import Visualizer
from config import PATH_CONFIG


class DataAugmentationPipeline:
    """数据增强流水线"""
    
    def __init__(self):
        self.cropper = RandomCropper()
        self.api_client = DoubaoAPIClient()
        self.quality_checker = QualityChecker()
        self.visualizer = Visualizer()
        self.logger = self._setup_logger()

        # 创建输出目录
        self._create_output_dirs()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("DataAugmentationPipeline")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            os.makedirs(PATH_CONFIG["logs_dir"], exist_ok=True)
            log_file = os.path.join(
                PATH_CONFIG["logs_dir"], 
                f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            )
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
            
        return logger
    
    def _create_output_dirs(self):
        """创建输出目录"""
        for dir_path in PATH_CONFIG.values():
            if dir_path.endswith('_dir'):
                os.makedirs(dir_path, exist_ok=True)

        # 创建可视化目录
        os.makedirs(os.path.join(PATH_CONFIG["output_dir"], "visualizations"), exist_ok=True)
    
    def load_text_file(self, text_path: str) -> str:
        """加载文本文件"""
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            self.logger.error(f"Failed to load text file {text_path}: {e}")
            return ""
    
    def get_image_text_pairs(self) -> List[Tuple[str, str]]:
        """获取图片-文本对，只处理s后缀的图片"""
        pairs = []

        images_dir = PATH_CONFIG["input_images"]
        texts_dir = PATH_CONFIG["input_texts"]

        # 遍历图片文件，只处理s后缀的
        for img_file in os.listdir(images_dir):
            if not img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue

            # 检查是否是s后缀的图片
            base_name = os.path.splitext(img_file)[0]
            if not base_name.endswith('_s'):
                continue

            # 构建对应的文本文件路径
            text_file = f"{base_name}_answer.txt"

            img_path = os.path.join(images_dir, img_file)
            text_path = os.path.join(texts_dir, text_file)

            if os.path.exists(text_path):
                pairs.append((img_path, text_path))
                self.logger.info(f"Found s-suffix image: {img_file}")
            else:
                self.logger.warning(f"Text file not found for {img_file}: {text_path}")

        return pairs

    def process_crops_with_realtime_save(self, full_text: str, crops: List[Dict],
                                       crops_dir: str, base_name: str) -> List[Dict]:
        """实时处理裁切图片并保存结果"""
        import json
        from config import PROMPT_TEMPLATE

        results = []

        print(f"\n🔄 开始逐个处理 {len(crops)} 个裁切图片...")

        for i, crop in enumerate(crops):
            crop_id = i + 1
            print(f"\n{'='*50}")
            print(f"📸 处理裁切图片 {crop_id}/{len(crops)}")
            print(f"{'='*50}")

            # 1. 打印发送给豆包的提示词
            prompt = PROMPT_TEMPLATE.format(full_text=full_text)
            print(f"\n📝 发送给豆包的提示词:")
            print(f"{'─'*50}")
            print(prompt)
            print(f"{'─'*50}")

            # 2. 调用API
            print(f"\n🤖 正在调用豆包API...")
            try:
                api_result = self.api_client.call_api(full_text, crop['img_b64'])
                print(f"✅ API调用成功")
            except Exception as e:
                print(f"❌ API调用失败: {e}")
                api_result = {
                    "raw_content": "",
                    "success": False,
                    "error": str(e)
                }

            # 3. 实时保存API结果
            result_file = os.path.join(crops_dir, f"{base_name}_crop_{crop_id:02d}_result.json")
            try:
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "crop_id": crop_id,
                        "crop_info": {
                            "bbox": crop.get('bbox', []),
                            "text_ratio": crop.get('text_ratio', 0)
                        },
                        "api_result": api_result,
                        "timestamp": datetime.now().isoformat()
                    }, f, ensure_ascii=False, indent=2)
                print(f"💾 结果已保存: {result_file}")
            except Exception as e:
                print(f"❌ 保存结果失败: {e}")

            # 4. 打印和保存豆包原始输出
            print(f"\n📥 豆包原始输出:")
            print(f"{'─'*50}")
            raw_content = api_result.get('raw_content', '')
            if api_result.get('success', False):
                print(f"状态: ✅ 成功")
                print(f"原始输出内容:")
                print(raw_content)

                # 保存原始输出到单独文件
                raw_output_file = os.path.join(crops_dir, f"{base_name}_crop_{crop_id:02d}_raw_output.txt")
                try:
                    with open(raw_output_file, 'w', encoding='utf-8') as f:
                        f.write(raw_content)
                    print(f"💾 原始输出已保存: {raw_output_file}")
                except Exception as e:
                    print(f"❌ 保存原始输出失败: {e}")
            else:
                print(f"状态: ❌ 失败")
                print(f"错误信息: {api_result.get('error', '未知错误')}")

                # 即使失败也保存错误信息
                error_file = os.path.join(crops_dir, f"{base_name}_crop_{crop_id:02d}_error.txt")
                try:
                    with open(error_file, 'w', encoding='utf-8') as f:
                        f.write(f"API调用失败\n错误信息: {api_result.get('error', '未知错误')}")
                    print(f"💾 错误信息已保存: {error_file}")
                except Exception as e:
                    print(f"❌ 保存错误信息失败: {e}")
            print(f"{'─'*50}")

            # 5. 添加到结果列表
            crop_result = {
                "crop_id": crop_id,
                "bbox": crop.get('bbox', []),
                "img_b64": crop['img_b64'],
                "text_ratio": crop.get('text_ratio', 0),
                "api_result": api_result,
                "result_file": result_file
            }
            results.append(crop_result)

            print(f"✅ 裁切图片 {crop_id} 处理完成")

        print(f"\n🎉 所有裁切图片处理完成! 共处理 {len(results)} 个")
        return results

    def process_single_pair(self, img_path: str, text_path: str) -> Dict:
        """处理单个图片-文本对 - 简化版"""
        self.logger.info(f"Processing: {os.path.basename(img_path)}")

        # 1. 加载文本
        full_text = self.load_text_file(text_path)
        if not full_text:
            return {"error": "Failed to load text"}

        # 2. 创建输出目录
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        crop_output_dir = os.path.join(PATH_CONFIG["output_dir"], "crops", base_name)
        vis_output_dir = os.path.join(PATH_CONFIG["output_dir"], "visualizations", base_name)

        # 3. 随机裁切并保存小图片
        try:
            crops = self.cropper.random_crops(
                img_path,
                save_crops=True,
                output_dir=crop_output_dir
            )
            self.logger.info(f"Generated {len(crops)} crops, saved to {crop_output_dir}")
        except Exception as e:
            self.logger.error(f"Cropping failed: {e}")
            return {"error": f"Cropping failed: {e}"}

        if not crops:
            return {"error": "No valid crops generated"}

        # 4. API识别 - 实时处理和保存
        try:
            api_results = self.process_crops_with_realtime_save(full_text, crops, crop_output_dir, base_name)
            self.logger.info(f"API processing completed")
        except Exception as e:
            self.logger.error(f"API processing failed: {e}")
            return {"error": f"API processing failed: {e}"}

        # 5. 生成可视化结果
        try:
            visualization_result = self.visualizer.create_detailed_visualization(
                img_path, crops, api_results, vis_output_dir, base_name
            )
            self.logger.info(f"Visualization created: {visualization_result['visualization_image']}")
        except Exception as e:
            self.logger.error(f"Visualization failed: {e}")
            visualization_result = {"error": f"Visualization failed: {e}"}

        # 6. 保存结果
        result = {
            "image_path": img_path,
            "text_path": text_path,
            "base_name": base_name,
            "full_text": full_text,
            "crops": crops,
            "crop_output_dir": crop_output_dir,
            "api_results": api_results,
            "visualization_result": visualization_result,
            "timestamp": datetime.now().isoformat()
        }

        return result
    
    def save_augmented_data(self, results: List[Dict]):
        """保存增强后的数据"""
        # 确保输出目录存在
        os.makedirs(PATH_CONFIG["output_dir"], exist_ok=True)

        output_file = os.path.join(
            PATH_CONFIG["output_dir"],
            f"augmented_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        # 统计可视化结果
        total_visualizations = sum(
            1 for r in results
            if "visualization_result" in r and "error" not in r["visualization_result"]
        )

        # 准备保存的数据
        save_data = {
            "metadata": {
                "total_images": len(results),
                "total_crops": sum(len(r.get("crops", [])) for r in results),
                "total_valid_annotations": sum(
                    r.get("quality_report", {}).get("usable_annotations", 0)
                    for r in results
                ),
                "total_visualizations": total_visualizations,
                "generation_time": datetime.now().isoformat()
            },
            "results": results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"Results saved to: {output_file}")
        
        # 生成统计报告
        self._generate_summary_report(save_data)
    
    def _generate_summary_report(self, data: Dict):
        """生成汇总报告"""
        metadata = data["metadata"]
        results = data["results"]
        
        # 统计质量分数
        quality_scores = [
            r.get("quality_report", {}).get("quality_score", 0) 
            for r in results if "quality_report" in r
        ]
        
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        # 统计状态分布
        total_ok = sum(
            r.get("quality_report", {}).get("status_distribution", {}).get("ok", 0)
            for r in results
        )
        total_skip = sum(
            r.get("quality_report", {}).get("status_distribution", {}).get("skip", 0)
            for r in results
        )
        total_error = sum(
            r.get("quality_report", {}).get("status_distribution", {}).get("error", 0)
            for r in results
        )
        
        report = f"""
数据增强流水线执行报告
========================

基本统计:
- 处理图片数量: {metadata['total_images']}
- 生成裁切数量: {metadata['total_crops']}
- 有效标注数量: {metadata['total_valid_annotations']}
- 生成可视化数量: {metadata.get('total_visualizations', 0)}
- 平均质量分数: {avg_quality:.3f}

标注状态分布:
- 成功 (ok): {total_ok}
- 跳过 (skip): {total_skip}
- 错误 (error): {total_error}

质量分析:
- 高质量 (>0.8): {sum(1 for s in quality_scores if s > 0.8)}
- 中等质量 (0.6-0.8): {sum(1 for s in quality_scores if 0.6 <= s <= 0.8)}
- 低质量 (<0.6): {sum(1 for s in quality_scores if s < 0.6)}

可视化结果:
- 可视化图片保存在: augmented_data/visualizations/
- 每张图片包含: 边界框标注图、详细信息JSON、HTML报告

生成时间: {metadata['generation_time']}
"""
        
        report_file = os.path.join(
            PATH_CONFIG["logs_dir"],
            f"summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        self.logger.info(f"Summary report saved to: {report_file}")
    
    def run_pipeline(self, max_images: int = None):
        """运行完整流水线"""
        self.logger.info("Starting data augmentation pipeline...")
        
        # 获取图片-文本对
        pairs = self.get_image_text_pairs()
        if max_images:
            pairs = pairs[:max_images]
        
        self.logger.info(f"Found {len(pairs)} image-text pairs")
        
        if not pairs:
            self.logger.error("No valid image-text pairs found")
            return
        
        # 处理每个对
        results = []
        failed_count = 0
        
        for img_path, text_path in tqdm(pairs, desc="Processing images"):
            try:
                result = self.process_single_pair(img_path, text_path)
                if "error" in result:
                    self.logger.error(f"Failed to process {img_path}: {result['error']}")
                    failed_count += 1
                else:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Unexpected error processing {img_path}: {e}")
                failed_count += 1
        
        # 保存结果
        if results:
            self.save_augmented_data(results)
            self.logger.info(f"Pipeline completed. Processed: {len(results)}, Failed: {failed_count}")
        else:
            self.logger.error("No results to save")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="手写OCR数据增强流水线")
    parser.add_argument("--max_images", type=int, default=None,
                       help="最大处理图片数量（用于测试）")
    parser.add_argument("--test", action="store_true",
                       help="测试模式，只处理前3张图片")

    args = parser.parse_args()

    pipeline = DataAugmentationPipeline()

    if args.test:
        print("运行测试模式...")
        pipeline.run_pipeline(max_images=3)
    else:
        pipeline.run_pipeline(max_images=args.max_images)


if __name__ == "__main__":
    main()
