{"crop_id": 3, "crop_info": {"bbox": [343, 486, 691, 1090], "text_ratio": 0.2702433965136637}, "api_result": {"raw_content": "```json\n[\n  {\"id\":1, \"text\":\"或医学奖获得者屠\"},\n  {\"id\":2, \"text\":\"着事事争第一的决\"},\n  {\"id\":3, \"text\":\"奋勇向前，最终发\"},\n  {\"id\":4, \"text\":\"变中国医学界的青\"},\n  {\"id\":5, \"text\":\"到了至高的荣誉。\"},\n  {\"id\":6, \"text\":\"的不懈追求，使她\"},\n  {\"id\":7, \"text\":\"的成功。\"},\n  {\"id\":8, \"text\":\"或完美的成就固然\"},\n  {\"id\":9, \"text\":\"若没有了完美，人\"},\n  {\"id\":10, \"text\":\"失去意义了吗？答\"},\n  {\"id\":11, \"text\":\"我们在地铁或公交\"},\n  {\"id\":12, \"text\":\"发现映入眼帘的多\"},\n  {\"id\":13, \"text\":\"生。世人中的天之\"},\n  {\"id\":14, \"text\":\"到完美的尽乎更少。\"},\n  {\"id\":15, \"text\":\"成。世间最多的伟\"}\n]\n```", "success": true, "attempt": 1}, "timestamp": "2025-07-29T18:35:29.554906"}