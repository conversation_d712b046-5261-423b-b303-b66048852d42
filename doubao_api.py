

"""
豆包API调用模块 - 集成火山引擎豆包1.6思考模型
"""

import json
import time
import asyncio
import logging
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from volcenginesdkarkruntime import Ark
from config import DOUBAO_CONFIG, PROMPT_TEMPLATE


class DoubaoAPIClient:
    """豆包API客户端"""

    def __init__(self, config=None):
        self.config = config or DOUBAO_CONFIG
        self.client = Ark(
            api_key=self.config["api_key"],
            timeout=self.config.get("timeout", 1800)
        )
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("DoubaoAPI")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def create_messages(self, full_text: str, crop_b64: str) -> List[Dict]:
        """创建API请求消息 - 只发送裁切后的小图片"""
        prompt = PROMPT_TEMPLATE.format(full_text=full_text)

        messages = [{
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{crop_b64}"
                    }
                },
                {
                    "type": "text",
                    "text": prompt
                }
            ]
        }]

        return messages
    
    def call_api(self, full_text: str, crop_b64: str,
                retry_count: int = 3) -> Optional[Dict]:
        """调用豆包API - 简化版，直接返回原始输出"""
        messages = self.create_messages(full_text, crop_b64)

        for attempt in range(retry_count):
            try:
                response = self.client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages
                )

                # 获取原始响应内容
                content = response.choices[0].message.content

                # 直接返回原始内容，不做过多处理
                return {
                    "raw_content": content,
                    "success": True,
                    "attempt": attempt + 1
                }

            except Exception as e:
                self.logger.error(f"API call error (attempt {attempt + 1}): {e}")

            # 重试前等待
            if attempt < retry_count - 1:
                time.sleep(1 * (attempt + 1))

        return {
            "raw_content": "",
            "success": False,
            "error": "All attempts failed"
        }



    def _normalize_response(self, response) -> Dict:
        """将响应标准化为统一格式"""
        if isinstance(response, list):
            # 新格式：JSON数组
            if len(response) == 0:
                return {
                    "status": "skip",
                    "lines": [],
                    "text": "",
                    "format": "array"
                }
            else:
                # 合并所有行文本
                combined_text = "\n".join(item["text"] for item in response)
                return {
                    "status": "ok",
                    "lines": response,
                    "text": combined_text,
                    "format": "array"
                }
        else:
            # 旧格式：兼容处理
            return {
                "status": response.get("status", "error"),
                "lines": [{"id": 1, "text": response.get("text", "")}] if response.get("status") == "ok" else [],
                "text": response.get("text", ""),
                "format": "legacy"
            }
    
    def _validate_response(self, response) -> bool:
        """验证API响应格式 - 支持新的JSON数组格式"""
        # 新格式：JSON数组，每个元素包含id和text
        if isinstance(response, list):
            # 空数组是有效的（表示没有完整行）
            if len(response) == 0:
                return True

            # 检查数组元素格式
            for item in response:
                if not isinstance(item, dict):
                    return False
                if "id" not in item or "text" not in item:
                    return False
                if not isinstance(item["id"], int) or not isinstance(item["text"], str):
                    return False
            return True

        # 兼容旧格式（向后兼容）
        if isinstance(response, dict):
            if "status" not in response:
                return False

            status = response["status"]
            if status not in ["ok", "skip"]:
                return False

            if status == "ok":
                if "text" not in response or not isinstance(response["text"], str):
                    return False
            elif status == "skip":
                response["text"] = response.get("text", "")

            return True

        return False
    
    def sequential_process(self, tasks: List[Dict]) -> List[Dict]:
        """逐个处理任务 - 简化版，直接保存原始输出"""
        results = []

        for i, task in enumerate(tasks):
            self.logger.info(f"Processing crop {i+1}/{len(tasks)}")

            result = self.call_api(
                task["full_text"],
                task["crop_b64"]
            )

            # 直接保存原始结果
            crop_result = {
                "crop_id": task.get("crop_id", i+1),
                "bbox": task["bbox"],
                "crop_filename": task.get("crop_filename", ""),
                "raw_output": result["raw_content"] if result else "",
                "api_success": result["success"] if result else False,
                "api_attempt": result.get("attempt", 0) if result else 0,
                "original_task": task
            }

            results.append(crop_result)

        return results
    
    def process_single_image(self, full_text: str, crops: List[Dict]) -> List[Dict]:
        """处理单张图片的所有裁切 - 简化版"""
        tasks = []
        for crop in crops:
            tasks.append({
                "full_text": full_text,
                "crop_b64": crop["img_b64"],
                "bbox": crop["bbox"],
                "crop_id": crop.get("crop_id", 0),
                "crop_filename": crop.get("crop_filename", "")
            })

        self.logger.info(f"Processing {len(tasks)} crops sequentially...")
        results = self.sequential_process(tasks)

        # 简单统计
        success_count = sum(1 for r in results if r["api_success"])
        self.logger.info(f"API calls: {success_count}/{len(results)} successful")

        return results


def test_doubao_api():
    """测试豆包API"""
    client = DoubaoAPIClient()
    
    # 测试数据
    test_text = """三、写作（60分）

20. 阅读下面的材料，按要求写作。（60分）

正在热播的一档音乐节目在宣传语里写道："赢，只是歌手的一段路；迎，才是音乐的目的地。"这句话谈的是音乐，也能引发音乐之外的思考。"""
    
    # 模拟裁切数据（这里用空的base64作为示例）
    test_crops = [{
        "bbox": [100, 100, 400, 200],
        "img_b64": "",  # 实际使用时需要真实的base64数据
        "text_ratio": 0.3
    }]
    
    print("测试豆包API...")
    # 注意：这个测试需要真实的图片base64数据才能正常工作
    # results = client.process_single_image(test_text, test_crops)
    # print(f"测试结果: {results}")
    print("API客户端初始化成功")


if __name__ == "__main__":
    test_doubao_api()
