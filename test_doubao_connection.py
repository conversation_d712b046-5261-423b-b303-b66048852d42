#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API连接测试脚本
"""

import sys
import json
from doubao_api import DoubaoAPIClient
from config import DOUBAO_CONFIG

def test_api_connection():
    """测试豆包API连接"""
    print("=" * 60)
    print("🧪 豆包API连接测试")
    print("=" * 60)
    
    # 显示配置信息（隐藏敏感信息）
    print("📋 当前配置:")
    print(f"   模型: {DOUBAO_CONFIG['model']}")
    print(f"   API密钥: {'*' * 20}{DOUBAO_CONFIG['api_key'][-8:] if len(DOUBAO_CONFIG['api_key']) > 8 else '***'}")
    print(f"   最大tokens: {DOUBAO_CONFIG['max_tokens']}")
    print(f"   温度: {DOUBAO_CONFIG['temperature']}")
    print()
    
    try:
        # 初始化API
        print("🔄 初始化豆包API...")
        api = DoubaoAPIClient()
        print("✅ API初始化成功")
        
        # 准备测试数据
        test_prompt = """# 角色
你是一名 OCR 多模态助手。

# 背景说明
- 你当前看到一张**已经随机裁切过的"子图"**，它仅代表原作文图片的一部分。
- 我已提供你这张子图对应的**完整 OCR 识别文本**，即原文全文（full_text）。

原文全文如下：
\"\"\"
这是一个测试文本，用于验证API连接是否正常。
\"\"\"

# 任务
请你严格从子图中识别所有**实际可见的文本**，输出的文字**必须严格来自 full_text**。
以下规则必须遵守：
1. **绝不添字，不得漏字**：子图上可见的字符必须全部识别；子图中未出现的字符，不可生成。
2. **不能输出图片上没有的内容或无关文本**：即使存在于 full_text 中，也不能输出。
3. 可以输出**断续片段**（如两个字、一行中间的数个字符），只要在子图里看得见，并且在 full_text 中逐字匹配。
4. **禁止 hallucination**，输出不得包含任何 full_text 之外的字符。
5. 不得输出空白、提示语或与作文无关内容。

# 输出格式
```json
[
  {"id":1,"text":"片段内容1"},
  {"id":2,"text":"片段内容2"}
]
```"""
        
        # 创建一个简单的测试图片（1x1像素的白色图片）
        import base64
        from PIL import Image
        import io
        
        # 创建一个简单的测试图片
        test_image = Image.new('RGB', (100, 50), color='white')
        buffer = io.BytesIO()
        test_image.save(buffer, format='PNG')
        test_image_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        print("🔄 发送测试请求...")
        
        # 调用API
        response = api.call_api("这是一个测试文本，用于验证API连接是否正常。", test_image_b64)
        
        print("\n📤 请求信息:")
        print(f"   提示词长度: {len(test_prompt)} 字符")
        print(f"   图片大小: {len(test_image_b64)} 字符 (base64)")

        print("\n📥 响应信息:")
        if isinstance(response, dict):
            print(f"   响应类型: 字典")
            for key, value in response.items():
                if key == 'raw_content' and len(str(value)) > 200:
                    print(f"   {key}: {str(value)[:200]}...")
                else:
                    print(f"   {key}: {value}")
        else:
            print(f"   响应类型: {type(response)}")
            print(f"   响应内容: {response}")

        # 检查是否成功
        if response.get('success', False):
            print("\n" + "=" * 60)
            print("🎉 豆包API连接测试成功!")
            print("✅ 系统已准备就绪，可以开始正式处理")
            print("=" * 60)
            return True
        else:
            print("\n" + "=" * 60)
            print("⚠️  API连接成功，但调用失败")
            print("❌ 可能的问题:")
            if "InvalidEndpointOrModel.NotFound" in str(response.get('error', '')):
                print("   - 模型ID不正确或没有访问权限")
                print("   - 请检查config.py中的模型名称")
                print("   - 常见的豆包模型ID: doubao-lite-4k, doubao-pro-4k, doubao-pro-32k")
            else:
                print(f"   - {response.get('error', '未知错误')}")
            print("=" * 60)
            return False
        
    except Exception as e:
        print(f"\n❌ API连接测试失败!")
        print(f"错误信息: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供一些常见问题的解决建议
        print("\n🔧 可能的解决方案:")
        print("1. 检查API密钥是否正确")
        print("2. 检查网络连接是否正常")
        print("3. 检查豆包API服务是否可用")
        print("4. 检查config.py中的配置是否正确")
        
        import traceback
        print(f"\n📋 详细错误信息:")
        traceback.print_exc()
        
        return False

def main():
    """主函数"""
    success = test_api_connection()
    
    if success:
        print("\n🚀 可以开始使用以下命令运行完整流水线:")
        print("   python run_pipeline.py")
    else:
        print("\n⚠️  请先解决API连接问题，然后重新测试")
        sys.exit(1)

if __name__ == "__main__":
    main()
