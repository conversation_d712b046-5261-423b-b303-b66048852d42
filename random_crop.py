"""
随机裁切模块 - 实现图片的智能随机裁切
"""

import os
import random
import base64
import io
import json
from typing import List, Tu<PERSON>, Dict
import numpy as np
from PIL import Image
import cv2
from config import CROP_CONFIG


class RandomCropper:
    """随机裁切器"""
    
    def __init__(self, config=None):
        self.config = config or CROP_CONFIG
        
    def calculate_iou(self, box1: Tuple[int, int, int, int], 
                     box2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的交并比(IoU)"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
            
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def estimate_text_ratio(self, image: Image.Image) -> float:
        """估算图片中的文字占比"""
        # 转换为灰度图
        gray = np.array(image.convert('L'))
        
        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 计算非白色像素比例（文字区域）
        text_pixels = np.sum(binary < 200)  # 非白色像素
        total_pixels = binary.size
        
        return text_pixels / total_pixels
    

    
    def generate_random_bbox(self, width: int, height: int,
                           existing_boxes: List[Tuple[int, int, int, int]]) -> Tuple[int, int, int, int]:
        """生成随机边界框，带边缘外扩以降低截半行风险"""
        scale_min, scale_max = self.config["scale_range"]
        area_min, area_max = self.config["area_range"]
        max_iou = self.config["max_iou"]
        expand = self.config["expand_bbox"]

        max_attempts = 50
        for _ in range(max_attempts):
            # 随机生成尺度
            scale = random.uniform(scale_min, scale_max)
            w = int(width * scale)
            h = int(height * scale)

            # 确保面积在合理范围内
            area_ratio = (w * h) / (width * height)
            if not (area_min <= area_ratio <= area_max):
                continue

            # 随机生成位置
            x1 = random.randint(0, max(0, width - w))
            y1 = random.randint(0, max(0, height - h))
            x2 = x1 + w
            y2 = y1 + h

            # 边缘外扩策略：四周各外扩expand像素，降低截半行概率
            x1_expanded = max(0, x1 - expand)
            y1_expanded = max(0, y1 - expand)
            x2_expanded = min(width, x2 + expand)
            y2_expanded = min(height, y2 + expand)

            new_box = (x1_expanded, y1_expanded, x2_expanded, y2_expanded)

            # 检查与现有框的重叠度
            valid = True
            for existing_box in existing_boxes:
                if self.calculate_iou(new_box, existing_box) > max_iou:
                    valid = False
                    break

            if valid:
                return new_box

        # 如果无法生成有效框，返回None
        return None
    
    def crop_image_to_base64(self, image: Image.Image,
                           bbox: Tuple[int, int, int, int]) -> str:
        """裁切图片并转换为base64"""
        x1, y1, x2, y2 = bbox
        cropped = image.crop((x1, y1, x2, y2))

        # 直接转换为base64，不进行质量增强
        buffer = io.BytesIO()
        cropped.save(buffer, format='PNG', quality=95)
        img_b64 = base64.b64encode(buffer.getvalue()).decode()

        return img_b64
    
    def random_crops(self, image_path: str, num_crops: int = None, save_crops: bool = True,
                    output_dir: str = None) -> List[Dict]:
        """对图片进行随机裁切，并保存每个裁切的小图片"""
        if num_crops is None:
            num_crops = self.config["num_crops_per_image"]

        # 读取图片
        image = Image.open(image_path).convert('RGB')
        width, height = image.size

        # 创建输出目录
        if save_crops and output_dir:
            os.makedirs(output_dir, exist_ok=True)

        crops = []
        existing_boxes = []
        text_threshold = self.config["text_ratio_threshold"]

        # 获取图片基础名称
        base_name = os.path.splitext(os.path.basename(image_path))[0]

        # 尝试生成指定数量的裁切
        max_attempts = num_crops * 4
        for attempt in range(max_attempts):
            if len(crops) >= num_crops:
                break

            # 生成随机边界框
            bbox = self.generate_random_bbox(width, height, existing_boxes)
            if bbox is None:
                continue

            # 裁切图片
            x1, y1, x2, y2 = bbox
            cropped_img = image.crop((x1, y1, x2, y2))

            # 简化检查：只要不是完全空白就保留
            if self._is_mostly_blank(cropped_img):
                continue

            # 转换为base64
            img_b64 = self.crop_image_to_base64(image, bbox)

            # 保存裁切的小图片
            crop_filename = None
            if save_crops and output_dir:
                crop_filename = f"{base_name}_crop_{len(crops)+1:02d}.png"
                crop_path = os.path.join(output_dir, crop_filename)
                cropped_img.save(crop_path, 'PNG')

            crop_info = {
                "crop_id": len(crops) + 1,
                "bbox": bbox,
                "text_ratio": self.estimate_text_ratio(cropped_img),
                "img_b64": img_b64,
                "size": (x2 - x1, y2 - y1),
                "crop_filename": crop_filename,
                "crop_path": os.path.join(output_dir, crop_filename) if crop_filename else None
            }

            crops.append(crop_info)
            existing_boxes.append(bbox)

        return crops

    def _is_mostly_blank(self, image: Image.Image) -> bool:
        """检查图片是否主要为空白"""
        # 转换为灰度
        gray = np.array(image.convert('L'))

        # 计算非白色像素比例（阈值235，更严格的空白检测）
        non_white_ratio = (gray < 235).mean()

        # 如果非白色像素少于5%，认为是空白
        return non_white_ratio < 0.05
    
    def save_crop_info(self, crops: List[Dict], output_path: str):
        """保存裁切信息"""
        crop_data = {
            "num_crops": len(crops),
            "crops": [
                {
                    "bbox": crop["bbox"],
                    "text_ratio": crop["text_ratio"],
                    "size": crop["size"]
                }
                for crop in crops
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(crop_data, f, ensure_ascii=False, indent=2)


def test_random_cropper():
    """测试随机裁切功能"""
    cropper = RandomCropper()
    
    # 测试图片路径
    test_image = "processed_data/images/2025050011_q.png"
    
    if os.path.exists(test_image):
        print(f"测试图片: {test_image}")
        crops = cropper.random_crops(test_image, num_crops=3)
        print(f"生成了 {len(crops)} 个裁切")
        
        for i, crop in enumerate(crops):
            print(f"裁切 {i+1}: bbox={crop['bbox']}, text_ratio={crop['text_ratio']:.3f}")
    else:
        print(f"测试图片不存在: {test_image}")


if __name__ == "__main__":
    test_random_cropper()
